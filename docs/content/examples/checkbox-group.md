---
title: Checkbox Group
tags:
  - Checkbox
  - Form controls
---

# Checkbox Group

<Description>

Rendering multiple Checkbox elements to form a group of checkboxes. The behavior is similar to having `v-model` for multiple native `input[type="checkbox"]`.

</Description>

<Tags />

<ComponentPreview type="example"  name="CheckboxGroup" />

<ExampleSection>

### Checkbox Group

Renders when the checkbox is in a checked or indeterminate state. You can style this element directly, or you can use it as a wrapper to put an icon into, or both.

</ExampleSection>
