<!-- This file was automatic generated. Do not edit it manually -->

<PropsTable :data="[
  {
    'name': 'as',
    'description': '<p>The element or component this component should render as. Can be overwritten by <code>asChild</code>.</p>\n',
    'type': 'AsTag | Component',
    'required': false,
    'default': '\'svg\''
  },
  {
    'name': 'asChild',
    'description': '<p>Change the default rendered element for the one passed as a child, merging their props and behavior.</p>\n<p>Read our <a href=\'https://www.reka-ui.com/docs/guides/composition\'>Composition</a> guide for more details.</p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'height',
    'description': '<p>The height of the arrow in pixels.</p>\n',
    'type': 'number',
    'required': false,
    'default': '5'
  },
  {
    'name': 'rounded',
    'description': '<p>When <code>true</code>, render the rounded version of arrow. Do not work with <code>as</code>/<code>asChild</code></p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'width',
    'description': '<p>The width of the arrow in pixels.</p>\n',
    'type': 'number',
    'required': false,
    'default': '10'
  }
]" />
