<!-- This file was automatic generated. Do not edit it manually -->

<PropsTable :data="[
  {
    'name': 'as',
    'description': '<p>The element or component this component should render as. Can be overwritten by <code>asChild</code>.</p>\n',
    'type': 'AsTag | Component',
    'required': false,
    'default': '\'input\''
  },
  {
    'name': 'asChild',
    'description': '<p>Change the default rendered element for the one passed as a child, merging their props and behavior.</p>\n<p>Read our <a href=\'https://www.reka-ui.com/docs/guides/composition\'>Composition</a> guide for more details.</p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'autoFocus',
    'description': '<p>Focus on element when mounted.</p>\n',
    'type': 'boolean',
    'required': false
  },
  {
    'name': 'maxLength',
    'description': '<p>Maximum number of character allowed.</p>\n',
    'type': 'number',
    'required': false
  },
  {
    'name': 'placeholder',
    'description': '<p>The placeholder character to use for empty tags input.</p>\n',
    'type': 'string',
    'required': false
  }
]" />
