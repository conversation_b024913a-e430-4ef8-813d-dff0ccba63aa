<!-- This file was automatic generated. Do not edit it manually -->

<PropsTable :data="[
  {
    'name': 'duration',
    'description': '<p>Time in milliseconds that each toast should remain visible for.</p>\n',
    'type': 'number',
    'required': false,
    'default': '5000'
  },
  {
    'name': 'label',
    'description': '<p>An author-localized label for each toast. Used to help screen reader users\nassociate the interruption with a toast.</p>\n',
    'type': 'string',
    'required': false,
    'default': '\'Notification\''
  },
  {
    'name': 'swipeDirection',
    'description': '<p>Direction of pointer swipe that should close the toast.</p>\n',
    'type': '\'right\' | \'left\' | \'up\' | \'down\'',
    'required': false,
    'default': '\'right\''
  },
  {
    'name': 'swipeThreshold',
    'description': '<p>Distance in pixels that the swipe must pass before a close is triggered.</p>\n',
    'type': 'number',
    'required': false,
    'default': '50'
  }
]" />
