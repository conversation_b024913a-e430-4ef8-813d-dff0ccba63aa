---

title: Aspect Ratio
description: Displays content within a desired ratio.
name: aspect-ratio
---

# Aspect Ratio

<Description>
Displays content within a desired ratio.
</Description>

<ComponentPreview type="demo" name="AspectRatio" />

## Features

<Highlights
  :features="[
    'Accepts any custom ratio.'
  ]"
/>

## Installation

Install the component from your command line.

<InstallationTabs value="reka-ui" />

## Anatomy

Import the component.

```vue
<script setup>
import { AspectRatio } from 'reka-ui'
</script>

<template>
  <AspectRatio />
</template>
```

## API Reference

### Root

Contains the content you want to constrain to a given ratio.

<!-- @include: @/meta/AspectRatio.md -->
