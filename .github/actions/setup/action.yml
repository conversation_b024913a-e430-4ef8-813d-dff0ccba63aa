name: Setup
description: Installs Node and caches pnpm.

runs:
  using: composite

  steps:
    - name: Setup Node.js environment
      uses: actions/setup-node@v4
      with:
        node-version: 18
        registry-url: 'https://registry.npmjs.org'

    - uses: pnpm/action-setup@v2
      name: Install pnpm
      with:
        version: 9
        run_install: false

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - uses: actions/cache@v4
      name: Setup pnpm cache
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      shell: bash
      run: pnpm i --frozen-lockfile
