name: Publish Any Commit (v2)
on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup (Install node & pnpm)
        uses: ./.github/actions/setup

      - name: Build Reka UI and plugins
        run: pnpm run build

      - run: pnpm dlx pkg-pr-new publish --compact ./packages/core
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
