name: 🐞 Bug report
description: Create a report to help us improve reka-ui.
title: '[Bug]: '
labels: [bug]
body:
  - type: markdown
    attributes:
      value: |
        **Before You Start...**

        This form is only for submitting bug reports. If you have a usage question
        or are unsure if this is really a bug, make sure to:

        - Read the [docs](https://reka-ui.com/)
        - Ask on [Discord Chat](https://chat.unovue.com/)
        - Ask on [GitHub Discussions](https://github.com/unovue/reka-ui/discussions)

        Also try to search for your issue - it may have already been answered or even fixed.
        However, if you find that an old, closed issue still persists in the latest version,
        you should open a new issue using the form below instead of commenting on the old issue.
  - type: textarea
    id: bug-env
    attributes:
      label: Environment
      description: Please provide the following information about your environment.
      value: |
        Developement/Production OS: Windows 10 19043.1110
        Node version: 16.0.0
        Package manager: pnpm@8.6.0
        Reka UI version: 2.0.0
        Vue version: 3.0.0
        Nuxt version: 3.0.0
        Nuxt mode: universal
        Nuxt target: server
        CSS framework: tailwindcss@3.3.3
        Client OS: Windows 10 19043.1110
        Browser: Chrome 90.0.4430.212
      render: bash
    validations:
      required: true
  - type: input
    id: reproduction-link
    attributes:
      label: Link to minimal reproduction
      description: |
        Please provide a link to a minimal reproduction of the bug.
        A minimal reproduction is a CodeSandbox, CodePen, or a StackBlitz that contains the bare minimum amount of code needed to show the bug.
        A minimal reproduction is required unless you are absolutely sure that the issue is obvious and the provided information is enough to understand the problem

        This is **required** for us to be able to triage your issue in a timely manner.

        Please do not just fill in a random link. The issue will be closed if no valid reproduction is provided.
      placeholder: Reproduction Link
    validations:
      required: true
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to reproduce
      description: |
        How do you trigger this bug? Please walk us through it step by step.
        Note that you can use [Markdown](https://guides.github.com/features/mastering-markdown/) to format lists and code.
      placeholder: Steps to reproduce
    validations:
      required: true
  - type: textarea
    id: bug-description
    attributes:
      label: Describe the bug
      description: A clear and concise description of what the bug is. If you intend to submit a PR for this issue, tell us in the description. Thanks!
      placeholder: Bug description
    validations:
      required: true
  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: A clear and concise description of what you expected to happen.
  - type: textarea
    id: screenshots
    attributes:
      label: Context & Screenshots (if applicable)
      description: |
        If applicable, provide any additional context or screenshots of the bug.
        You can drag and drop images here to add them to the issue.
