name: 🚀 Feature request
description: Suggest a feature that will improve reka-ui.
title: '[Feature]: '
labels: [feature request]
body:
  - type: markdown
    attributes:
      value: |
        Thank you for taking the time to fill out this feature request!
  - type: textarea
    id: feature-description
    attributes:
      label: Describe the feature
      description: A clear and concise description of what you think would be a helpful addition to reka-ui, including the possible use cases and alternatives you have considered. If you have a working prototype or module that implements it, please include a link.
      placeholder: Feature description
    validations:
      required: true
  - type: checkboxes
    id: additional-info
    attributes:
      label: Additional information
      description: Additional information that helps us decide how to proceed.
      options:
        - label: I intend to submit a PR for this feature.
        - label: I have already implemented and/or tested this feature.
    validations:
      required: false
